/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700;800&display=swap');

/* Reset CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Open Sans', sans-serif;
}

/* Variabel warna */
:root {
  --bg-color: #f4f4f4;
  --text-color: #5a5a5a;
  --nav-bg: #1f5faa;
  --nav-hover: #2b83ea;
  --footer-bg: #1d1d1d;
  --card-bg: #e4e4e5;
  --shadow-light: 0px 2px 5px rgba(0, 0, 0, 0.1);
}

/* Body utama */
body {
  background: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 400;
}

/* Typography - Bold Text Support */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700 !important;
  margin-bottom: 15px;
}

.font-bold,
.fw-bold,
strong,
b {
  font-weight: 700 !important;
}

.font-semibold,
.fw-semibold {
  font-weight: 600 !important;
}

.font-medium,
.fw-medium {
  font-weight: 500 !important;
}

/* Labels and Important Text */
label {
  font-weight: 600 !important;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.table th {
  font-weight: 700 !important;
}

.btn {
  font-weight: 500 !important;
}

/* Content areas bold support */
.article-content strong,
.article-content b,
.content-text strong,
.content-text b,
textarea strong,
textarea b {
  font-weight: 700 !important;
}

/* Container Utama */
#container {
  max-width: 980px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  box-shadow: 0 0 1em #cccccc;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: transparent;
}

/* Header */
header {
  padding: 20px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.header-logo {
  height: 50px;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

header h1 {
  color: #b5b5b5;
  margin: 0;
}

/* Navigasi */
nav {
  background: var(--nav-bg);
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding: 10px;
}

nav a {
  color: #ffffff;
  text-decoration: none;
  padding: 12px 18px;
  font-weight: bold;
  transition: background 0.2s;
}

nav a.active,
nav a:hover {
  background: var(--nav-hover);
  border-radius: 5px;
}

/* Hero Section */
#hero {
  background: var(--card-bg);
  padding: 50px 20px;
  margin-bottom: 20px;
  border-radius: 12px;
}

#hero h1 {
  font-size: 35px;
  color: var(--text-color);
}

/* Wrapper Main & Sidebar */
#content-wrapper {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
  flex: 1;
}

/* Main Content */
#main {
  flex: 2;
  min-width: 0;
  background: transparent;
  padding: 20px;
  border-radius: 12px;
}

/* Sidebar */
#sidebar {
  flex: 1;
  min-width: 240px;
  max-width: 300px;
  background: var(--card-bg);
  padding: 20px;
  border-radius: 12px;
}

/* Widget Style */
.widget-box {
  background: var(--card-bg);
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: var(--shadow-light);
}

.widget-box h3 {
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.widget-box ul {
  list-style: none;
}

.widget-box ul li a {
  text-decoration: none;
  color: var(--nav-bg);
  font-weight: 500;
  transition: color 0.2s;
}

.widget-box ul li a:hover {
  color: var(--nav-hover);
}

/* Footer */
footer {
  text-align: center;
  padding: 20px;
  background: var(--footer-bg);
  color: #eee;
  margin-top: auto;
}

/* Table */
table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

table th,
table td {
  border: 1px solid #ddd;
  padding: 12px 15px;
  text-align: left;
}

table th {
  background: var(--nav-bg);
  color: white;
}

table tr:nth-child(even) {
  background: #f2f2f2;
}

/* Tombol */
.btn-read-more,
.btn-back {
  display: inline-block;
  padding: 10px 15px;
  background-color: #3b5066;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  transition: background 0.2s;
}

.btn-read-more:hover,
.btn-back:hover {
  background-color: var(--nav-hover);
}

/* Image styling - Responsive for all aspect ratios */
.table img,
table img,
td img,
tbody img {
    max-width: 50px;
    max-height: 40px;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    padding: 1px;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
    margin: 0 auto;
}

.table img:hover,
table img:hover,
td img:hover,
tbody img:hover {
    transform: scale(1.2);
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

/* Form preview images - Flexible for all sizes */
.image-preview img,
.form-group img,
.modal img {
    max-width: 200px;
    max-height: 150px;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 6px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    padding: 2px;
    display: block;
    margin: 8px auto;
}

/* Responsive */
@media (max-width: 768px) {
  #content-wrapper {
    flex-direction: column;
  }

  #sidebar {
    max-width: 100%;
  }

  nav {
    flex-direction: column;
    align-items: center;
  }
}
