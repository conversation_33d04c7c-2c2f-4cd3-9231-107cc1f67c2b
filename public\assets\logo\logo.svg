<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1f5faa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2b83ea;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background rounded rectangle -->
  <rect x="0" y="0" width="200" height="50" rx="8" ry="8" fill="url(#logoGradient)" />

  <!-- Newspaper icon -->
  <g fill="white" transform="translate(15, 12)">
    <!-- Paper outline -->
    <rect x="0" y="0" width="20" height="26" rx="2" fill="white" stroke="none"/>
    <!-- Header line -->
    <rect x="2" y="3" width="16" height="2" fill="#1f5faa"/>
    <!-- Content lines -->
    <rect x="2" y="8" width="12" height="1.5" fill="#666"/>
    <rect x="2" y="11" width="16" height="1.5" fill="#666"/>
    <rect x="2" y="14" width="10" height="1.5" fill="#666"/>
    <rect x="2" y="17" width="14" height="1.5" fill="#666"/>
    <rect x="2" y="20" width="8" height="1.5" fill="#666"/>
  </g>

  <!-- Logo text -->
  <text x="110" y="32" font-family="Arial, sans-serif" font-size="16" font-weight="bold"
        text-anchor="middle" fill="white">ARTICLE HUB</text>
</svg>
