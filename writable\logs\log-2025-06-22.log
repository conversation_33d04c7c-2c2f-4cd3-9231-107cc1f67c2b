DEBUG - 2025-06-22 12:59:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 12:59:59 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:00:00 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:00:00 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:00:00 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:47:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:47:06 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:47:06 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:47:06 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:47:06 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:47:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:47:09 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:47:09 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:47:09 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:47:09 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:52:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:52:20 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:52:20 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:20 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:20 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:52:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:52:23 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:52:23 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:23 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:23 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:52:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:52:25 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:52:25 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:25 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:25 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:52:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:52:27 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:52:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:27 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:52:27 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:53:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:53:12 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:53:12 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:53:12 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:53:12 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 13:57:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 13:57:41 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 13:57:41 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:57:41 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 13:57:41 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:17:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:17:25 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:17:25 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:17:25 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:17:25 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:18:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:18:31 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:18:31 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:18:31 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:18:31 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:18:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:18:58 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:18:58 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:18:58 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:18:58 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:19:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:19:46 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:19:46 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:19:46 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:19:46 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:19:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:19:49 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:19:49 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:19:49 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:19:49 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:20:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:20:53 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:20:53 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:20:53 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:20:53 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:23:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:23:59 --> mysqli_sql_exception: Unknown column 'created_at' in 'order clause' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:23:59 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:23:59 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:23:59 --> [Caused by] mysqli_sql_exception: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:26:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:26:07 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:26:07 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:26:07 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:26:07 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:27:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:27:11 --> mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:27:11 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:27:11 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:27:11 --> [Caused by] mysqli_sql_exception: Table 'sukses.article' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `article`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:29:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-22 14:29:35 --> mysqli_sql_exception: Unknown column 'created_at' in 'order clause' in D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 D:\XAMPP\htdocs\ci4\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 D:\XAMPP\htdocs\ci4\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 D:\XAMPP\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 D:\XAMPP\htdocs\ci4\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 D:\XAMPP\htdocs\ci4\system\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
#6 D:\XAMPP\htdocs\ci4\app\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
#7 D:\XAMPP\htdocs\ci4\system\View\Cell.php(307): App\Cells\ArticleLatest->render(NULL)
#8 D:\XAMPP\htdocs\ci4\system\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', Array, 'App\\Cells\\Artic...')
#9 D:\XAMPP\htdocs\ci4\system\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\Artic...', Array, 0, 'AppCellsArticle...')
#10 D:\XAMPP\htdocs\ci4\app\Views\template\footer.php(4): view_cell('App\\Cells\\Artic...', Array)
#11 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#12 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#13 D:\XAMPP\htdocs\ci4\system\View\View.php(475): CodeIgniter\View\View->render('template/footer', NULL, true)
#14 D:\XAMPP\htdocs\ci4\app\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
#15 D:\XAMPP\htdocs\ci4\system\View\View.php(224): include('D:\\XAMPP\\htdocs...')
#16 D:\XAMPP\htdocs\ci4\system\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#17 D:\XAMPP\htdocs\ci4\system\Common.php(1173): CodeIgniter\View\View->render('home', Array, true)
#18 D:\XAMPP\htdocs\ci4\app\Controllers\Page.php(7): view('home', Array)
#19 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#20 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#21 D:\XAMPP\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#22 D:\XAMPP\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#23 D:\XAMPP\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#24 D:\XAMPP\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#25 D:\XAMPP\htdocs\ci4\system\rewrite.php(44): require_once('D:\\XAMPP\\htdocs...')
#26 {main}
CRITICAL - 2025-06-22 14:29:35 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 6 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 7 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 8 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
 9 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
10 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
11 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
12 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
13 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
14 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
15 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
16 APPPATH\Controllers\Page.php(7): view('home', [...])
17 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
18 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
19 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
20 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
21 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
22 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
23 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:29:35 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 8 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 9 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
10 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
11 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
12 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
13 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
14 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
15 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
16 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
17 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
18 APPPATH\Controllers\Page.php(7): view('home', [...])
19 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
20 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
21 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
22 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
23 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
24 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
25 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
CRITICAL - 2025-06-22 14:29:35 --> [Caused by] mysqli_sql_exception: Unknown column 'created_at' in 'order clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `artikel`
ORDER BY `created_at` DESC
 LIMIT 5', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(675): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Cells\ArticleLatest.php(21): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 9 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
10 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
11 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
12 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
13 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
14 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
15 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
16 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
17 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
18 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
19 APPPATH\Controllers\Page.php(7): view('home', [...])
20 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
21 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
22 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
23 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
24 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
25 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
26 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:30:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:30:48 --> ErrorException: Undefined array key "title"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 7.
 1 APPPATH\Views\components\article_latest.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(24): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:32:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:32:46 --> ErrorException: Undefined array key "title"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 7.
 1 APPPATH\Views\components\article_latest.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(24): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:34:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:34:43 --> ErrorException: Undefined array key "title"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 7.
 1 APPPATH\Views\components\article_latest.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(24): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:36:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:36:27 --> ErrorException: Undefined array key "title"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 7.
 1 APPPATH\Views\components\article_latest.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(24): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:37:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:37:12 --> ErrorException: Undefined array key "title"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 7.
 1 APPPATH\Views\components\article_latest.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(25): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:38:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:38:06 --> ErrorException: Undefined array key "title"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 7.
 1 APPPATH\Views\components\article_latest.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(25): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:40:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:40:19 --> ErrorException: Undefined array key "category"
[Method: GET, Route: /]
in APPPATH\Views\components\article_latest.php on line 15.
 1 APPPATH\Views\components\article_latest.php(15): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "category"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php', 15)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\components\\article_latest.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/article_latest', [], true)
 5 APPPATH\Cells\ArticleLatest.php(25): view('components/article_latest', [...])
 6 SYSTEMPATH\View\Cell.php(307): App\Cells\ArticleLatest->render(null)
 7 SYSTEMPATH\View\Cell.php(104): CodeIgniter\View\Cell->renderSimpleClass(Object(App\Cells\ArticleLatest), 'render', [...], 'App\\Cells\\ArticleLatest')
 8 SYSTEMPATH\Common.php(1189): CodeIgniter\View\Cell->render('App\\Cells\\ArticleLatest::render', [...], 0, 'AppCellsArticleLatestrendercd0369f38ecc0b16ce1b3606f36d8a61')
 9 APPPATH\Views\template\footer.php(4): view_cell('App\\Cells\\ArticleLatest::render', [...])
10 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\template\\footer.php')
11 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
12 SYSTEMPATH\View\View.php(475): CodeIgniter\View\View->render('template/footer', null, true)
13 APPPATH\Views\home.php(16): CodeIgniter\View\View->include('template/footer')
14 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\home.php')
15 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
16 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('home', [], true)
17 APPPATH\Controllers\Page.php(7): view('home', [...])
18 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
19 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
20 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
21 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
22 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
23 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
24 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:41:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:41:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:41:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:41:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:41:46 --> ErrorException: Undefined array key "image"
[Method: GET, Route: article]
in APPPATH\Views\article\index.php on line 6.
 1 APPPATH\Views\article\index.php(6): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "image"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php', 6)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:41:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:41:54 --> ErrorException: Undefined array key "image"
[Method: GET, Route: article]
in APPPATH\Views\article\index.php on line 6.
 1 APPPATH\Views\article\index.php(6): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "image"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php', 6)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:42:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:42:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:42:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:42:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:42:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:42:36 --> ErrorException: Undefined array key "image"
[Method: GET, Route: article]
in APPPATH\Views\article\index.php on line 6.
 1 APPPATH\Views\article\index.php(6): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "image"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php', 6)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:43:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:43:31 --> ErrorException: Undefined array key "image"
[Method: GET, Route: article]
in APPPATH\Views\article\index.php on line 6.
 1 APPPATH\Views\article\index.php(6): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "image"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php', 6)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:46:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-22 14:46:42 --> ErrorException: Undefined array key "content"
[Method: GET, Route: article]
in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "content"', 'D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php', 7)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-22 14:48:10 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:10 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:10 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-22 14:48:15 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:15 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:15 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:48:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-22 14:48:37 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:37 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:37 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:48:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-22 14:48:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-22 14:48:41 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:41 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:48:41 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:50:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-22 14:50:59 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:50:59 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
WARNING - 2025-06-22 14:50:59 --> [DEPRECATED] substr(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Views\article\index.php on line 7.
 1 APPPATH\Views\article\index.php(7): substr(null, 0, 200)
 2 SYSTEMPATH\View\View.php(224): include('D:\\XAMPP\\htdocs\\ci4\\app\\Views\\article\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('article/index', [], true)
 5 APPPATH\Controllers\Article.php(16): view('article/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Article->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Article))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('D:\\XAMPP\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-06-22 14:52:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
