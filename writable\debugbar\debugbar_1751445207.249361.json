{"url": "http://localhost:8080/index.php/", "method": "GET", "isAJAX": false, "startTime": **********.990384, "totalTime": 167.1, "totalMemory": "5.401", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.998083, "duration": 0.057890892028808594}, {"name": "Required Before Filters", "component": "Timer", "start": **********.055978, "duration": 0.005686044692993164}, {"name": "Routing", "component": "Timer", "start": **********.061674, "duration": 0.002482891082763672}, {"name": "Before Filters", "component": "Timer", "start": **********.065132, "duration": 0.00011515617370605469}, {"name": "Controller", "component": "Timer", "start": **********.065252, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.065254, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.155738, "duration": 9.775161743164062e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.155804, "duration": 0.0018360614776611328}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "1.46 ms", "sql": "<strong>SELECT</strong> `artikel`.*, `kategori`.`nama_kategori`\n<strong>FROM</strong> `artikel`\n<strong>LEFT</strong> <strong>JOIN</strong> `kategori` <strong>ON</strong> `kategori`.`id_kategori` = `artikel`.`id_kategori`\n<strong>ORDER</strong> <strong>BY</strong> `artikel`.`id` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:20", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Page.php:17", "function": "        App\\Models\\ArtikelModel->getArtikelDenganKategori()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Page->home()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\ci4\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:20", "qid": "01ef082ddc12ffe5d88bff2e2e096b90"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.096064, "duration": "0.025573"}, {"name": "Query", "component": "Database", "start": **********.12397, "duration": "0.001459", "query": "<strong>SELECT</strong> `artikel`.*, `kategori`.`nama_kategori`\n<strong>FROM</strong> `artikel`\n<strong>LEFT</strong> <strong>JOIN</strong> `kategori` <strong>ON</strong> `kategori`.`id_kategori` = `artikel`.`id_kategori`\n<strong>ORDER</strong> <strong>BY</strong> `artikel`.`id` <strong>DESC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/default.php", "component": "Views", "start": **********.137384, "duration": 0.016456127166748047}, {"name": "View: home.php", "component": "Views", "start": **********.136185, "duration": 0.018857955932617188}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 151 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Page.php", "name": "Page.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "APPPATH\\Models\\KategoriModel.php", "name": "KategoriModel.php"}, {"path": "APPPATH\\Views\\home.php", "name": "home.php"}, {"path": "APPPATH\\Views\\layout\\default.php", "name": "default.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 151, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Page", "method": "home", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Page::home"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\Artikel::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "tos", "handler": "\\App\\Controllers\\Page::tos"}, {"method": "GET", "route": "oauth-setup", "handler": "\\App\\Controllers\\Page::oauthSetup"}, {"method": "GET", "route": "debug/session", "handler": "\\App\\Controllers\\Debug::session"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/register", "handler": "\\App\\Controllers\\User::register"}, {"method": "GET", "route": "user/verify-email/(.*)", "handler": "\\App\\Controllers\\User::verifyEmail/$1"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "user/dashboard", "handler": "\\App\\Controllers\\UserDashboard::index"}, {"method": "GET", "route": "user/profile", "handler": "\\App\\Controllers\\UserDashboard::profile"}, {"method": "GET", "route": "auth/google", "handler": "\\App\\Controllers\\GoogleAuth::login"}, {"method": "GET", "route": "auth/google/callback", "handler": "\\App\\Controllers\\GoogleAuth::callback"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\GoogleAuth::logout"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "artikel/([^/]+)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "post", "handler": "\\App\\Controllers\\Post::index"}, {"method": "GET", "route": "post/new", "handler": "\\App\\Controllers\\Post::new"}, {"method": "GET", "route": "post/(.*)/edit", "handler": "\\App\\Controllers\\Post::edit/$1"}, {"method": "GET", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::show/$1"}, {"method": "GET", "route": "kate<PERSON>i", "handler": "\\App\\Controllers\\Kategori::index"}, {"method": "GET", "route": "kategori/new", "handler": "\\App\\Controllers\\Kategori::new"}, {"method": "GET", "route": "kate<PERSON>i/(.*)/edit", "handler": "\\App\\Controllers\\Kategori::edit/$1"}, {"method": "GET", "route": "kate<PERSON>i/(.*)", "handler": "\\App\\Controllers\\Kategori::show/$1"}, {"method": "GET", "route": "fix-slug", "handler": "\\App\\Controllers\\FixSlug::index"}, {"method": "GET", "route": "add-kate<PERSON>i", "handler": "\\App\\Controllers\\AddKategori::index"}, {"method": "GET", "route": "add-game-category", "handler": "\\App\\Controllers\\AddGameCategory::index"}, {"method": "GET", "route": "reset-auto-increment", "handler": "\\App\\Controllers\\ResetAutoIncrement::index"}, {"method": "GET", "route": "reset-to-one", "handler": "\\App\\Controllers\\ResetToOne::index"}, {"method": "GET", "route": "delete-all-articles", "handler": "\\App\\Controllers\\ResetToOne::deleteAll"}, {"method": "GET", "route": "reset-options", "handler": "\\App\\Controllers\\ResetOptions::index"}, {"method": "GET", "route": "debug-artikel", "handler": "\\App\\Controllers\\DebugArtikel::index"}, {"method": "GET", "route": "debug-logs", "handler": "\\App\\Controllers\\DebugArtikel::logs"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "user/register", "handler": "\\App\\Controllers\\User::register"}, {"method": "POST", "route": "post", "handler": "\\App\\Controllers\\Post::create"}, {"method": "POST", "route": "kate<PERSON>i", "handler": "\\App\\Controllers\\Kategori::create"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::update/$1"}, {"method": "PATCH", "route": "kate<PERSON>i/(.*)", "handler": "\\App\\Controllers\\Kategori::update/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::update/$1"}, {"method": "PUT", "route": "kate<PERSON>i/(.*)", "handler": "\\App\\Controllers\\Kategori::update/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "post/(.*)", "handler": "\\App\\Controllers\\Post::delete/$1"}, {"method": "DELETE", "route": "kate<PERSON>i/(.*)", "handler": "\\App\\Controllers\\Kategori::delete/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 40, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "25.04", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.06", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.030924, "duration": 0.0250399112701416}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.125445, "duration": 5.507469177246094e-05}]}], "vars": {"varData": {"View Data": {"title": "Home", "content": "Welcome to our website. We strive to provide valuable information and a seamless experience.", "artikel": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (6)</li><li>Contents (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>judul</th><th>isi</th><th>gambar</th><th>status</th><th>slug</th><th>category</th><th>created_at</th><th>title</th><th>image</th><th>content</th><th>id_kategori</th><th>nama_kategori</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">9</td><td title=\"UTF-8 string (51)\">🚗 Otomotif: Dunia Mesin yang Terus Bergerak Maju</td><td title=\"UTF-8 string (1293)\">Otomotif adalah bidang yang berkaitan dengan desain, pengembangan, produksiUTF-8</td><td title=\"string (35)\">1751398993_92d738113909955475aa.jpg</td><td title=\"string (1)\">1</td><td title=\"string (45)\">otomotif-dunia-mesin-yang-terus-bergerak-maju</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-02 02:43:13</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (2)\">25</td><td title=\"string (8)\">Otomotif</td></tr><tr><th>1</th><td title=\"string (1)\">8</td><td title=\"UTF-8 string (79)\">&#9917; Sepak Bola: Olahraga Paling Populer di Dunia yang Menyatukan Semua Kalangan</td><td title=\"UTF-8 string (1667)\">Sepak bola adalah olahraga yang paling banyak dimainkan dan ditonton di selUTF-8</td><td title=\"string (35)\">1751398712_fc0608703f43780645d1.jpg</td><td title=\"string (1)\">1</td><td title=\"string (74)\">sepak-bola-olahraga-paling-populer-di-dunia-yang-menyatukan-semua-kalangan</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-02 02:35:58</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">6</td><td title=\"string (8)\">Olahraga</td></tr><tr><th>2</th><td title=\"string (1)\">7</td><td title=\"UTF-8 string (52)\">🌍 Traveler: Menjelajah Dunia, Menyerap Pengalaman</td><td title=\"UTF-8 string (1534)\">Seorang traveler bukan hanya orang yang suka bepergian. Traveler adalah sosUTF-8</td><td title=\"string (35)\">1751398299_aa3b4b1e6a1a10c3ff2b.jpg</td><td title=\"string (1)\">1</td><td title=\"string (45)\">traveler-menjelajah-dunia-menyerap-pengalaman</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-02 02:31:39</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">8</td><td title=\"string (6)\">Travel</td></tr><tr><th>3</th><td title=\"string (1)\">6</td><td title=\"UTF-8 string (76)\">💡 Teknologi: Perkembangan, Manfaat, dan Dampaknya dalam Kehidupan Manusia</td><td title=\"string (716)\">Teknologi adalah penerapan ilmu pengetahuan untuk tujuan praktis dalam kehiUTF-8</td><td title=\"string (35)\">1751398032_80836246de6698ac1a78.jpg</td><td title=\"string (1)\">1</td><td title=\"string (68)\">teknologi-perkembangan-manfaat-dan-dampaknya-dalam-kehidupan-manusia</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-02 02:27:12</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (2)\">13</td><td title=\"string (9)\">Teknologi</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (72)\">🍽&#65039; Kulineran: Menjelajahi Rasa, Budaya, dan Cerita di Setiap Suapan</td><td title=\"UTF-8 string (1226)\">📌 Apa Itu Kulineran?\r\nKulineran bukan sekadar aktivitas makan&#8212;itu adalah peUTF-8</td><td title=\"string (35)\">1751375383_a9938257c156abe4361e.jpg</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (65)\">&#65039;-kulineran-menjelajahi-rasa-budaya-dan-cerita-di-setiap-suapan</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-01 20:09:43</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">9</td><td title=\"string (7)\">Kuliner</td></tr><tr><th>5</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (61)\">🩺 Kesehatan: Kunci Utama Menuju Kehidupan yang Berkualitas</td><td title=\"UTF-8 string (839)\">Kesehatan adalah kondisi sejahtera secara fisik, mental, dan sosial, bukan UTF-8</td><td title=\"string (35)\">1751374232_4983cf9fd36b454d7e27.jpg</td><td title=\"string (1)\">1</td><td title=\"string (55)\">kesehatan-kunci-utama-menuju-kehidupan-yang-berkualitas</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-01 19:50:32</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">7</td><td title=\"string (9)\">Kesehatan</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>UTF-8 string</var> (51) \"🚗 Otomotif: Dunia Mesin yang Terus Bergerak Maju\"<div class=\"access-path\">$value[0]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>UTF-8 string</var> (1293) \"Otomotif adalah bidang yang berkaitan dengan desain, pengembangan, produksi,...<div class=\"access-path\">$value[0]['isi']</div></dt><dd><pre>Otomotif adalah bidang yang berkaitan dengan desain, pengembangan, produksi, perawatan, dan penggunaan kendaraan bermotor, seperti mobil, motor, truk, dan bus. Dunia otomotif sangat luas dan terus berkembang pesat seiring kemajuan teknologi. Dari kendaraan berbahan bakar bensin hingga mobil listrik dan otonom, industri otomotif selalu bergerak mengikuti kebutuhan zaman.\r\n📌 Sejarah Singkat Otomotif\r\nIndustri otomotif dimulai sejak akhir abad ke-19, saat mobil pertama kali diciptakan oleh Karl Benz di Jerman. Seiring waktu, mobil menjadi alat transportasi yang umum di seluruh dunia. Inovasi demi inovasi muncul, mulai dari mesin yang lebih efisien, sistem rem canggih, hingga kendaraan otomatis tanpa pengemudi.\r\n🌍 Masa Depan Otomotif\r\nMasa depan otomotif sangat dipengaruhi oleh isu lingkungan, efisiensi energi, dan digitalisasi. Kendaraan berbahan bakar fosil mulai ditinggalkan. Mobil listrik, mobil tenaga surya, dan kendaraan berbasis AI adalah tren yang akan mendominasi.\r\n&#9989; Kesimpulan\r\nOtomotif bukan hanya soal kendaraan, tapi juga soal teknologi, gaya hidup, dan kemajuan zaman. Dunia otomotif menyatukan mesin dan inovasi dalam satu harmoni. Baik kamu sebagai pengguna, teknisi, pebisnis, atau sekadar hobi&#8212;otomotif selalu punya ruang untuk dieksplorasi dan dikagumi.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (35) \"1751398993_92d738113909955475aa.jpg\"<div class=\"access-path\">$value[0]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (45) \"otomotif-dunia-mesin-yang-terus-bergerak-maju\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['category']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-02 02:43:13\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id_kategori</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[0]['id_kategori']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>nama_kategori</dfn> =&gt; <var>string</var> (8) \"Otomotif\"<div class=\"access-path\">$value[0]['nama_kategori']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>UTF-8 string</var> (79) \"&#9917; Sepak Bola: Olahraga Paling Populer di Dunia yang Menyatukan Semua Kalangan\"<div class=\"access-path\">$value[1]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>UTF-8 string</var> (1667) \"Sepak bola adalah olahraga yang paling banyak dimainkan dan ditonton di selu...<div class=\"access-path\">$value[1]['isi']</div></dt><dd><pre>Sepak bola adalah olahraga yang paling banyak dimainkan dan ditonton di seluruh dunia. Dengan jutaan penggemar dari berbagai benua, sepak bola bukan sekadar pertandingan 11 lawan 11, tapi juga bagian dari budaya, semangat, dan identitas masyarakat global.\r\n🏟&#65039; Sejarah Singkat Sepak Bola\r\nAkar sepak bola modern berasal dari Inggris pada abad ke-19, meski bentuk awal permainan ini sudah ada sejak zaman Tiongkok kuno dan Romawi. Organisasi sepak bola tertinggi dunia, FIFA (F&#233;d&#233;ration Internationale de Football Association), berdiri pada tahun 1904 dan kini menaungi kompetisi paling prestisius: Piala Dunia.\r\n🧠 Aturan dan Format Permainan\r\nPermainan berlangsung selama 2 babak masing-masing 45 menit, dengan jeda istirahat 15 menit. Setiap tim terdiri dari 11 pemain, termasuk 1 kiper. Tujuannya sederhana: mencetak gol sebanyak mungkin ke gawang lawan.\r\n🌟 Pemain Legendaris\r\nSepak bola telah melahirkan ikon-ikon dunia seperti:\r\n&gt;Pel&#233; dan Diego Maradona (Brasil &amp; Argentina)\r\n&gt;Cristiano Ronaldo dan Lionel Messi (Portugal &amp; Argentina)\r\n&gt;Zinedine Zidane, Ronaldinho, hingga Mohamed Salah di era modern\r\nMereka tak hanya dikenal karena kemampuan teknik tinggi, tapi juga pengaruh luar biasa dalam dunia olahraga dan sosial.\r\n🏆 Sepak Bola dan Masyarakat\r\nSepak bola mampu menyatukan berbagai latar belakang. Di desa hingga kota besar, dari anak-anak hingga orang tua, sepak bola adalah hiburan, semangat, dan kadang jadi bagian dari perjuangan identitas suatu negara atau komunitas.\r\nDi Indonesia, antusiasme terhadap sepak bola sangat tinggi. Klub-klub seperti Persija Jakarta, Arema FC, dan Persebaya Surabaya punya basis suporter besar yang loyal.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (35) \"1751398712_fc0608703f43780645d1.jpg\"<div class=\"access-path\">$value[1]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (74) \"sepak-bola-olahraga-paling-populer-di-dunia-yang-menyatukan-semua-kalangan\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['category']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-02 02:35:58\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id_kategori</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[1]['id_kategori']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>nama_kategori</dfn> =&gt; <var>string</var> (8) \"Olahraga\"<div class=\"access-path\">$value[1]['nama_kategori']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>UTF-8 string</var> (52) \"🌍 Traveler: Menjelajah Dunia, Menyerap Pengalaman\"<div class=\"access-path\">$value[2]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>UTF-8 string</var> (1534) \"Seorang traveler bukan hanya orang yang suka bepergian. Traveler adalah soso...<div class=\"access-path\">$value[2]['isi']</div></dt><dd><pre>Seorang traveler bukan hanya orang yang suka bepergian. Traveler adalah sosok yang berani melangkah keluar dari zona nyamannya, menjelajah tempat-tempat baru, dan membuka mata pada dunia yang lebih luas. Di setiap langkahnya, selalu ada hal yang bisa dipelajari&#8212;entah itu budaya, cara hidup orang lain, atau sekadar menghargai keindahan alam.\r\n\r\nMenjadi traveler tidak harus keliling dunia. Menjelajahi desa di pinggir kota, naik kereta ke tempat yang belum pernah dikunjungi, atau menyusuri pantai yang belum ramai pun bisa menjadi pengalaman berharga. Yang penting bukan seberapa jauh perginya, tapi seberapa dalam kita menikmati dan memahami perjalanan itu.\r\n\r\nDalam setiap perjalanan, ada tantangan. Capek di jalan, bingung arah, bahkan kadang sendirian. Tapi justru di situlah kekuatan seorang traveler diuji. Mereka belajar sabar, mandiri, dan tangguh. Mereka bertemu orang baru, belajar kebiasaan lokal, mencicipi makanan yang tak biasa, dan membawa pulang bukan cuma oleh-oleh, tapi juga cerita.\r\n\r\nTraveler sejati bukan hanya datang untuk mengambil foto, tapi juga untuk menghargai tempat yang dikunjunginya. Mereka belajar untuk menjaga alam, menghormati budaya, dan membawa pulang nilai-nilai yang membuat mereka jadi pribadi yang lebih terbuka dan bijak.\r\n\r\nDi dunia yang luas ini, jadi traveler adalah cara untuk terus belajar tanpa harus duduk di kelas. Dunia adalah bukunya, dan setiap tempat yang dikunjungi adalah halamannya. Semakin banyak tempat dijelajahi, semakin banyak pula pelajaran yang bisa didapatkan.\r\n\r\n\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (35) \"1751398299_aa3b4b1e6a1a10c3ff2b.jpg\"<div class=\"access-path\">$value[2]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (45) \"traveler-menjelajah-dunia-menyerap-pengalaman\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['category']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-02 02:31:39\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id_kategori</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[2]['id_kategori']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>nama_kategori</dfn> =&gt; <var>string</var> (6) \"Travel\"<div class=\"access-path\">$value[2]['nama_kategori']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>UTF-8 string</var> (76) \"💡 Teknologi: Perkembangan, Manfaat, dan Dampaknya dalam Kehidupan Manusia\"<div class=\"access-path\">$value[3]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (716) \"Teknologi adalah penerapan ilmu pengetahuan untuk tujuan praktis dalam kehid...<div class=\"access-path\">$value[3]['isi']</div></dt><dd><pre>Teknologi adalah penerapan ilmu pengetahuan untuk tujuan praktis dalam kehidupan manusia. Secara umum, teknologi mencakup alat, metode, proses, dan sistem yang diciptakan manusia untuk mempermudah pekerjaan, meningkatkan efisiensi, dan menyelesaikan berbagai permasalahan. Teknologi berkembang seiring waktu, mulai dari teknologi sederhana seperti alat pertanian tradisional, hingga teknologi canggih seperti kecerdasan buatan dan robotika saat ini.\r\n\r\nDalam kehidupan sehari-hari, kita dikelilingi oleh berbagai bentuk teknologi: dari smartphone yang kita genggam, internet yang kita gunakan, kendaraan yang kita tumpangi, hingga sistem listrik yang menerangi rumah. Semuanya merupakan hasil perkembangan teknologi.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (35) \"1751398032_80836246de6698ac1a78.jpg\"<div class=\"access-path\">$value[3]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (68) \"teknologi-perkembangan-manfaat-dan-dampaknya-dalam-kehidupan-manusia\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['category']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-02 02:27:12\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id_kategori</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[3]['id_kategori']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>nama_kategori</dfn> =&gt; <var>string</var> (9) \"Teknologi\"<div class=\"access-path\">$value[3]['nama_kategori']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>UTF-8 string</var> (72) \"🍽&#65039; Kulineran: Menjelajahi Rasa, Budaya, dan Cerita di Setiap Suapan\"<div class=\"access-path\">$value[4]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>UTF-8 string</var> (1226) \"📌 Apa Itu Kulineran? Kulineran bukan sekadar aktivitas makan&#8212;itu adalah petu...<div class=\"access-path\">$value[4]['isi']</div></dt><dd><pre>📌 Apa Itu Kulineran?\r\nKulineran bukan sekadar aktivitas makan&#8212;itu adalah petualangan rasa, eksplorasi budaya, dan pengalaman sosial yang menyenangkan. Entah itu mencicipi makanan kaki lima di pinggir jalan, menikmati makanan khas daerah, atau nongkrong di caf&#233; hits, kulineran selalu punya cerita.\r\nDi Indonesia sendiri, kulineran sudah jadi gaya hidup, terutama bagi generasi muda yang gemar berbagi pengalaman lewat media sosial.\r\n🍜 Ragam Kuliner Indonesia yang Kaya Rasa\r\nIndonesia dikenal sebagai surganya kuliner. Setiap daerah punya ciri khas makanan dengan cita rasa yang kuat dan unik, seperti:\r\n1.Rendang dari Sumatera Barat, makanan terenak versi CNN.\r\n2.Pempek dari Palembang dengan kuah cuko khas.\r\n3.Soto Betawi, Gudeg Jogja, Rawon Surabaya, hingga Coto Makassar.\r\nSetiap hidangan membawa cerita sejarah, adat, dan kebiasaan lokal yang masih hidup sampai sekarang.\r\n💬 Penutup\r\nKulineran adalah bagian dari gaya hidup yang terus berkembang. Di balik setiap makanan, ada rasa, cerita, dan makna. Maka, jangan ragu untuk mencoba hal baru, eksplor rasa unik, dan dukung UMKM kuliner lokal. Karena, siapa tahu makanan favoritmu selanjutnya tersembunyi di warung kecil pinggir jalan yang belum kamu datangi!\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (35) \"1751375383_a9938257c156abe4361e.jpg\"<div class=\"access-path\">$value[4]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>UTF-8 string</var> (65) \"&#65039;-kulineran-menjelajahi-rasa-budaya-dan-cerita-di-setiap-suapan\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['category']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-01 20:09:43\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id_kategori</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[4]['id_kategori']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>nama_kategori</dfn> =&gt; <var>string</var> (7) \"Kuliner\"<div class=\"access-path\">$value[4]['nama_kategori']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>UTF-8 string</var> (61) \"🩺 Kesehatan: Kunci Utama Menuju Kehidupan yang Berkualitas\"<div class=\"access-path\">$value[5]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>UTF-8 string</var> (839) \"Kesehatan adalah kondisi sejahtera secara fisik, mental, dan sosial, bukan h...<div class=\"access-path\">$value[5]['isi']</div></dt><dd><pre>Kesehatan adalah kondisi sejahtera secara fisik, mental, dan sosial, bukan hanya bebas dari penyakit. Definisi ini berasal dari Organisasi Kesehatan Dunia (WHO) dan mencakup berbagai aspek kehidupan manusia.\r\nKesehatan bukan hanya tanggung jawab tenaga medis, tetapi juga hasil dari gaya hidup, lingkungan, pola makan, serta kesadaran individu dan masyarakat.\r\n🍎 Kesehatan Fisik\r\nKesehatan fisik berarti tubuh berfungsi dengan baik dan kuat melawan penyakit.\r\n🧠 Kesehatan Mental\r\nKesehatan mental sama pentingnya dengan fisik. Ini mencakup emosi, pikiran, dan kemampuan menghadapi stres.\r\nKesehatan adalah modal utama dalam menjalani hidup. Dengan menjaga pola hidup sehat dan sadar akan pentingnya pencegahan, kita bisa menjalani kehidupan yang lebih panjang, produktif, dan bahagia. Ingat, sehat itu bukan pilihan&#8212;tapi kebutuhan.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (35) \"1751374232_4983cf9fd36b454d7e27.jpg\"<div class=\"access-path\">$value[5]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (55) \"kesehatan-kunci-utama-menuju-kehidupan-yang-berkualitas\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['category']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-01 19:50:32\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id_kategori</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[5]['id_kategori']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>nama_kategori</dfn> =&gt; <var>string</var> (9) \"Kesehatan\"<div class=\"access-path\">$value[5]['nama_kategori']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1751444955</pre>", "_ci_previous_url": "http://localhost:8080/index.php/", "user_id": "2", "google_id": "105450807226409324209", "email": "<EMAIL>", "name": "<PERSON><PERSON>", "avatar": "https://lh3.googleusercontent.com/a/ACg8ocIHN-cyWcHBPkImICyXbYEMkyBcL_Yb7J5ln2k1xmaNeSpWrnWu=s96-c", "role": "admin", "logged_in": "<pre>1</pre>", "login_type": "google"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Dnt": "1", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7", "Cookie": "debug-bar-state=minimized; ci_session=7ecdde25aeb619326ff4320c40d86ae6"}, "cookies": {"debug-bar-state": "minimized", "ci_session": "7ecdde25aeb619326ff4320c40d86ae6"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}