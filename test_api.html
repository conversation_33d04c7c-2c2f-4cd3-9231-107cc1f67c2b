<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Test API CI4</h1>
    
    <div id="results"></div>
    
    <script>
        const apiUrl = 'http://localhost/ci4/public';
        const results = document.getElementById('results');
        
        // Test GET /post
        async function testGetPosts() {
            try {
                console.log('Testing GET /post...');
                const response = await axios.get(apiUrl + '/post');
                console.log('GET /post success:', response.data);
                results.innerHTML += '<h3>✅ GET /post - SUCCESS</h3>';
                results.innerHTML += '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                console.error('GET /post error:', error);
                results.innerHTML += '<h3>❌ GET /post - ERROR</h3>';
                results.innerHTML += '<pre>' + error.message + '</pre>';
                if (error.response) {
                    results.innerHTML += '<pre>' + JSON.stringify(error.response.data, null, 2) + '</pre>';
                }
            }
        }
        
        // Test GET /kategori
        async function testGetKategori() {
            try {
                console.log('Testing GET /kategori...');
                const response = await axios.get(apiUrl + '/kategori');
                console.log('GET /kategori success:', response.data);
                results.innerHTML += '<h3>✅ GET /kategori - SUCCESS</h3>';
                results.innerHTML += '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                console.error('GET /kategori error:', error);
                results.innerHTML += '<h3>❌ GET /kategori - ERROR</h3>';
                results.innerHTML += '<pre>' + error.message + '</pre>';
                if (error.response) {
                    results.innerHTML += '<pre>' + JSON.stringify(error.response.data, null, 2) + '</pre>';
                }
            }
        }
        
        // Test POST /post (create artikel)
        async function testCreatePost() {
            try {
                console.log('Testing POST /post...');
                const testData = {
                    judul: 'Test Artikel dari API Test',
                    isi: 'Ini adalah **test artikel** dari API test.\n\nDengan *formatting* yang bagus.',
                    status: 1,
                    id_kategori: 1
                };
                
                const response = await axios.post(apiUrl + '/post', testData);
                console.log('POST /post success:', response.data);
                results.innerHTML += '<h3>✅ POST /post - SUCCESS</h3>';
                results.innerHTML += '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
            } catch (error) {
                console.error('POST /post error:', error);
                results.innerHTML += '<h3>❌ POST /post - ERROR</h3>';
                results.innerHTML += '<pre>' + error.message + '</pre>';
                if (error.response) {
                    results.innerHTML += '<pre>' + JSON.stringify(error.response.data, null, 2) + '</pre>';
                }
            }
        }
        
        // Run all tests
        async function runAllTests() {
            results.innerHTML = '<h2>🧪 Running API Tests...</h2>';
            
            await testGetPosts();
            await testGetKategori();
            await testCreatePost();
            
            results.innerHTML += '<h2>🏁 Tests Completed!</h2>';
        }
        
        // Auto run tests when page loads
        window.onload = runAllTests;
    </script>
</body>
</html>
