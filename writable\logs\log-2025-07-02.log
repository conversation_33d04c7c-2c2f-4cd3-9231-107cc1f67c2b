DEBUG - 2025-07-02 05:46:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:46:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:46:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:47:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:47:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:47:08 --> Auth Filter - Session data: {"__ci_last_regenerate":1751435213,"_ci_previous_url":"http:\/\/localhost:8080\/index.php\/user\/login","user_id":"2","google_id":"105450807226409324209","email":"<EMAIL>","name":"<PERSON><PERSON>","avatar":"https:\/\/lh3.googleusercontent.com\/a\/ACg8ocIHN-cyWcHBPkImICyXbYEMkyBcL_Yb7J5ln2k1xmaNeSpWrnWu=s96-c","role":"admin","logged_in":true,"login_type":"google"}
DEBUG - 2025-07-02 05:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:47:10 --> Auth Filter - Session data: {"__ci_last_regenerate":1751435213,"_ci_previous_url":"http:\/\/localhost:8080\/index.php\/admin\/artikel","user_id":"2","google_id":"105450807226409324209","email":"<EMAIL>","name":"Andika Setiawan","avatar":"https:\/\/lh3.googleusercontent.com\/a\/ACg8ocIHN-cyWcHBPkImICyXbYEMkyBcL_Yb7J5ln2k1xmaNeSpWrnWu=s96-c","role":"admin","logged_in":true,"login_type":"google"}
DEBUG - 2025-07-02 05:50:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:51:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:55:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 05:57:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 06:00:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 06:00:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 06:00:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 06:42:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-07-02 07:51:34 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:51:34 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:51:53 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:51:53 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:51:54 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:51:54 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:53:43 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:53:43 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:53:46 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:53:46 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:54:02 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:54:02 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:54:04 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:54:04 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
ERROR - 2025-07-02 07:55:14 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\xampp\\htdocs...', 201)
#1 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#5 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#6 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#9 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('your_hosting_da...', 'your_database_u...', Object(SensitiveParameterValue), 'your_database_n...', 3306, '', 0)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#4 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#8 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known.  in C:\xampp\htdocs\ci4\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\ci4\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\ci4\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel...', Array, false)
#3 C:\xampp\htdocs\ci4\app\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
#4 C:\xampp\htdocs\ci4\app\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
#5 C:\xampp\htdocs\ci4\system\CodeIgniter.php(933): App\Controllers\Page->home()
#6 C:\xampp\htdocs\ci4\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
#7 C:\xampp\htdocs\ci4\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 C:\xampp\htdocs\ci4\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#9 C:\xampp\htdocs\ci4\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#10 C:\xampp\htdocs\ci4\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#11 C:\xampp\htdocs\ci4\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#12 {main}
CRITICAL - 2025-07-02 07:55:14 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: php_network_getaddresses: getaddrinfo for your_hosting_database_host failed: No such host is known. 
[Method: GET, Route: /]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `artikel`.*, `kategori`.`nama_kategori`
FROM `artikel`
LEFT JOIN `kategori` ON `kategori`.`id_kategori` = `artikel`.`id_kategori`
ORDER BY `artikel`.`id` DESC', [], false)
 3 APPPATH\Models\ArtikelModel.php(20): CodeIgniter\Database\BaseBuilder->get()
 4 APPPATH\Controllers\Page.php(17): App\Models\ArtikelModel->getArtikelDenganKategori()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Page->home()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Page))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\ci4\\public\\index.php')
DEBUG - 2025-07-02 07:57:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:57:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:57:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:57:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:58:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:58:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:58:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:58:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 07:58:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:24:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:24:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:24:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:24:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:24:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:24:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:25:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:25:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:28:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:28:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:28:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:28:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:28:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:28:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-07-02 08:29:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
