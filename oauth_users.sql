-- =============================================
-- Google OAuth Users Table
-- =============================================

CREATE TABLE IF NOT EXISTS `oauth_users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `google_id` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `avatar` text DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 1,
  `role` enum('admin','user') DEFAULT 'user',
  `status` enum('active','inactive','banned') DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `google_id` (`google_id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_google_id` (`google_id`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- Sample Data (Optional)
-- =============================================

-- Insert sample admin user (replace with your Google account)
-- INSERT INTO `oauth_users` (`google_id`, `email`, `name`, `role`, `status`, `created_at`, `updated_at`) 
-- VALUES ('sample_google_id', '<EMAIL>', 'Your Name', 'admin', 'active', NOW(), NOW());

-- =============================================
-- Indexes for Performance
-- =============================================

-- Additional indexes for better performance
CREATE INDEX `idx_last_login` ON `oauth_users` (`last_login`);
CREATE INDEX `idx_created_at` ON `oauth_users` (`created_at`);
CREATE INDEX `idx_role_status` ON `oauth_users` (`role`, `status`);
